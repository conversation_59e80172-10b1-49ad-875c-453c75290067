# API Usage Guide

*Auto-generated from backend API - Last updated: 2025-07-23T08:18:03.349Z*

## Installation

// Install dependencies
npm install

// The API client is already available in your project at:
// src/lib/api-client.ts
// src/types/api.ts

## Basic Usage

```typescript
import { apiClient, TypedApiClient } from './lib/api-client';

// Use the default instance
const response = await apiClient.getHealth();
console.log(response.data);

// Or create a custom instance
const customClient = new TypedApiClient({
  baseUrl: 'https://your-api-domain.com',
  timeout: 15000,
});
```

## Authentication

```typescript
// Use better-auth built-in client for auth
import { betterAuthClient } from '@/lib/better-auth-client';

// Sign up with email
await betterAuthClient.signUpWithEmail({
  email: '<EMAIL>',
  password: 'securePassword123',
  name: '<PERSON>',
});

// Sign in with email
await betterAuthClient.signInWithEmail({
  email: '<EMAIL>',
  password: 'securePassword123',
});

// Get current session & user
const { session, user } = await betterAuthClient.getSession();

// Sign out
await betterAuthClient.signOut();
```

## Examples


### Authentication

```typescript
import { betterAuthClient } from '@/lib/better-auth-client';

await betterAuthClient.signUpWithEmail({ email: '<EMAIL>', password: 'securePassword123' });
await betterAuthClient.signInWithEmail({ email: '<EMAIL>', password: 'securePassword123' });
const { session } = await betterAuthClient.getSession();
```


### Working with Listings

```typescript
// Get all listings with pagination and filters
const listingsResponse = await apiClient.getListings({
  page: 1,
  limit: 20,
  status: 'active',
  min_price: 50000,
  max_price: 500000
});

console.log('Listings:', listingsResponse.data);

// Create a new listing
const newListing = {
  business_name: "Coffee Shop Downtown",
  industry: "Food & Beverage",
  asking_price: 150000,
  annual_revenue: 200000,
  cash_flow_sde: 45000,
  general_location: "Downtown Seattle",
  year_established: 2015,
  employees: 5,
  team_visibility: "all"
};

const createResponse = await apiClient.createListing(newListing);
console.log('Created listing:', createResponse.data);

// Get a specific listing
const listingId = createResponse.data.listing.id;
const listingResponse = await apiClient.getListing(listingId);
console.log('Listing details:', listingResponse.data);
```


### File Upload

```typescript
// Upload a file
const fileInput = document.getElementById('file-input') as HTMLInputElement;
const file = fileInput.files[0];

const formData = new FormData();
formData.append('file', file);
formData.append('file_type', 'document');
formData.append('entity_type', 'listing');
formData.append('entity_id', listingId);

const uploadResponse = await apiClient.uploadFile(formData);
console.log('File uploaded:', uploadResponse.data);

// Get file details
const fileResponse = await apiClient.getFile(uploadResponse.data.file.id);
console.log('File details:', fileResponse.data);
```


### Error Handling

```typescript
try {
  const response = await apiClient.getUserProfile();
  console.log('Profile:', response.data);
} catch (error) {
  if (error instanceof Error) {
    console.error('API Error:', error.message);
    
    // Handle specific error cases
    if (error.message.includes('401')) {
      // Unauthorized - token expired or invalid
      apiClient.clearAuth();
      // Redirect to login or refresh token
    } else if (error.message.includes('404')) {
      // Resource not found
      console.log('Profile not found');
    } else if (error.message.includes('timeout')) {
      // Request timeout
      console.log('Request timed out, please try again');
    }
  }
}
```


### Custom Configuration

```typescript
// Create a client with custom configuration
const productionClient = new TypedApiClient({
  baseUrl: 'https://api.yourcompany.com',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'X-API-Version': '1.0',
    'X-Client-Version': '2.1.0'
  }
});

// Update configuration at runtime
productionClient.setBaseUrl('https://staging-api.yourcompany.com');
productionClient.setAuth('new-token');

// Get current configuration
const config = productionClient.getConfig();
console.log('Current config:', config);
```

