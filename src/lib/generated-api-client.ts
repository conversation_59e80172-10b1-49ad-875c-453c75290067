/**
 * This file was auto-generated from the backend API.
 * Do not make direct changes to the file.
 * 
 * Last updated: 2025-07-23T08:18:03.349Z
 * API Version: 1.0.0
 */

import type { paths, components } from "../types/api";

// Base API client configuration
export interface ApiClientConfig {
  baseUrl: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// Default configuration
const DEFAULT_CONFIG: ApiClientConfig = {
  baseUrl: 'http://localhost:3001',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Generic response wrapper
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// Error response type
export interface ApiError {
  message: string;
  status: number;
  details?: any;
}

export class ApiClient {
  protected config: ApiClientConfig;

  constructor(config: Partial<ApiClientConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Helper method to make HTTP requests
  protected async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      // Start with config headers
      let finalHeaders: Record<string, string> = { ...this.config.headers };
      
      // If body is FormData, remove Content-Type to let browser set multipart boundary
      if (options.body instanceof FormData) {
        finalHeaders = Object.fromEntries(
          Object.entries(finalHeaders).filter(([key]) => 
            key.toLowerCase() !== 'content-type'
          )
        );
      }
      
      // Merge with options headers (ensure it's a proper headers object)
      if (options.headers) {
        const optionsHeaders = options.headers as Record<string, string>;
        finalHeaders = {
          ...finalHeaders,
          ...optionsHeaders,
        };
      }
      
      const response = await fetch(url, {
        ...options,
        headers: finalHeaders,
        signal: controller.signal,
        credentials: 'include', // Include cookies for authentication
      });

      clearTimeout(timeoutId);

      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      let data: T;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = (await response.text()) as T;
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`Request timeout after ${this.config.timeout}ms`);
        }
        throw error;
      }
      
      throw new Error('Unknown error occurred');
    }
  }

  // GET request
  async get<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'GET',
      headers,
    });
  }

  // POST request
  async post<T>(
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: body ? JSON.stringify(body) : undefined,
      headers,
    });
  }

  // PUT request
  async put<T>(
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: body ? JSON.stringify(body) : undefined,
      headers,
    });
  }

  // DELETE request
  async delete<T>(endpoint: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
      headers,
    });
  }

  // PATCH request
  async patch<T>(
    endpoint: string,
    body?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: body ? JSON.stringify(body) : undefined,
      headers,
    });
  }

  // Set authorization header
  setAuth(token: string): void {
    this.config.headers = {
      ...this.config.headers,
      Authorization: `Bearer ${token}`,
    };
  }

  // Remove authorization header
  clearAuth(): void {
    if (this.config.headers?.Authorization) {
      delete this.config.headers.Authorization;
    }
  }

  // Update base URL
  setBaseUrl(baseUrl: string): void {
    this.config.baseUrl = baseUrl;
  }

  // Get current configuration
  getConfig(): ApiClientConfig {
    return { ...this.config };
  }
}

// Typed API methods using generated types
export class TypedApiClient extends ApiClient {
  // Health check
  async getHealth() {
    return this.get<components["schemas"]["HealthCheck"]>('/health');
  }

  // API index
  async getApiIndex() {
    return this.get<components["schemas"]["ApiIndex"]>('/');
  }

  // Auth endpoints
  async signUp(data: components["schemas"]["SignUpRequest"]) {
    return this.post<components["schemas"]["SignUpResponse"]>(
      '/v1/auth/signup',
      data
    );
  }

  async signIn(data: components["schemas"]["SignInRequest"]) {
    return this.post<components["schemas"]["SignInResponse"]>(
      '/v1/auth/signin',
      data
    );
  }

  async signOut(data: components["schemas"]["SignOutRequest"]) {
    return this.post<components["schemas"]["SignOutResponse"]>(
      '/v1/auth/signout',
      data
    );
  }

  async refreshToken(data: components["schemas"]["RefreshRequest"]) {
    return this.post<components["schemas"]["RefreshResponse"]>(
      '/v1/auth/refresh',
      data
    );
  }

  async forgotPassword(data: components["schemas"]["ForgotPasswordRequest"]) {
    return this.post<components["schemas"]["ForgotPasswordResponse"]>(
      '/v1/auth/forgot-password',
      data
    );
  }

  async resetPassword(data: components["schemas"]["ResetPasswordRequest"]) {
    return this.post<components["schemas"]["ResetPasswordResponse"]>(
      '/v1/auth/reset-password',
      data
    );
  }

  // User endpoints
  async getUserProfile() {
    return this.get<components["schemas"]["UserProfileResponse"]>(
      '/profile'
    );
  }

  async updateUserProfile(data: components["schemas"]["UpdateProfileRequest"]) {
    return this.put<components["schemas"]["UpdateProfileResponse"]>(
      '/profile',
      data
    );
  }

  async verifyEmail(data: components["schemas"]["VerifyEmailRequest"]) {
    return this.post<components["schemas"]["VerifyEmailResponse"]>(
      '/verify-email',
      data
    );
  }

  // Workspace endpoints
  async getCurrentWorkspace() {
    return this.get<components["schemas"]["SingleWorkspaceResponse"]>(
      '/v1/workspaces/current'
    );
  }

  async updateCurrentWorkspace(data: components["schemas"]["UpdateWorkspaceRequest"]) {
    return this.put<components["schemas"]["WorkspaceUpdateResponse"]>(
      '/v1/workspaces/current',
      data
    );
  }

  // Workspace invitation endpoints
  async getWorkspaceInvitations(params?: Record<string, string | number>) {
    const searchParams = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : '';
    return this.get<components["schemas"]["WorkspaceInvitationListResponse"]>(
      `/v1/workspaces/invitations${searchParams}`
    );
  }

  async createWorkspaceInvitation(data: components["schemas"]["CreateWorkspaceInvitationRequest"]) {
    return this.post<components["schemas"]["SingleWorkspaceInvitationResponse"]>(
      '/v1/workspaces/invitations',
      data
    );
  }

  async updateWorkspaceInvitation(
    id: string,
    data: components["schemas"]["UpdateWorkspaceInvitationRequest"]
  ) {
    return this.put<components["schemas"]["SingleWorkspaceInvitationResponse"]>(
      `/v1/workspaces/invitations/${id}`,
      data
    );
  }

  async deleteWorkspaceInvitation(id: string) {
    return this.delete<components["schemas"]["DeleteResponse"]>(
      `/v1/workspaces/invitations/${id}`
    );
  }

  async resendWorkspaceInvitation(id: string) {
    return this.post<components["schemas"]["SingleWorkspaceInvitationResponse"]>(
      `/v1/workspaces/invitations/${id}/resend`,
      {}
    );
  }

  // Listings endpoints
  async getListings(params?: Record<string, string | number>) {
    const searchParams = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : '';
    return this.get<components["schemas"]["ListingListResponse"]>(
      `/v1/listings${searchParams}`
    );
  }

  async getListing(id: string, params?: Record<string, string>) {
    const searchParams = params ? '?' + new URLSearchParams(params).toString() : '';
    return this.get<components["schemas"]["SingleListingResponse"]>(
      `/v1/listings/${id}${searchParams}`
    );
  }

  async createListing(data: components["schemas"]["CreateListingRequest"]) {
    return this.post<components["schemas"]["SingleListingResponse"]>(
      '/v1/listings',
      data
    );
  }

  async updateListing(
    id: string,
    data: components["schemas"]["UpdateListingRequest"]
  ) {
    return this.put<components["schemas"]["SingleListingResponse"]>(
      `/v1/listings/${id}`,
      data
    );
  }

  async deleteListing(id: string) {
    return this.delete<components["schemas"]["DeleteResponse"]>(
      `/v1/listings/${id}`
    );
  }

  async saveDraftListing(data: components["schemas"]["SaveDraftListingRequest"]) {
    return this.post<components["schemas"]["SingleListingResponse"]>(
      '/v1/listings/draft',
      data
    );
  }

  // File endpoints
  async uploadFile(data: FormData) {
    return this.request<components["schemas"]["UploadFileResponse"]>(
      '/v1/files/upload',
      {
        method: 'POST',
        body: data,
      }
    );
  }

  async uploadListingsCsv(data: FormData) {
    return this.request<components["schemas"]["BulkCreateResponse"]>(
      '/v1/listings/bulk/csv',
      {
        method: 'POST',
        body: data,
      }
    );
  }

  async getFile(id: string) {
    return this.get<components["schemas"]["GetFileResponse"]>(
      `/v1/files/${id}`
    );
  }

  async deleteFile(id: string) {
    return this.delete<components["schemas"]["DeleteResponse"]>(
      `/v1/files/${id}`
    );
  }
}

// Default export - create a singleton instance
export const apiClient = new TypedApiClient();

// Also export the class for custom instances
export default TypedApiClient; 