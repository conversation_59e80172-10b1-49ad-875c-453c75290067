import { createAuthClient } from 'better-auth/react';
import { organizationClient } from 'better-auth/client/plugins';
import type { components as BAComponents } from '@/types/better-auth-built-in-api';

// Better Auth OpenAPI-derived types
export type BAUser = BAComponents['schemas']['User'];
export type BASession = BAComponents['schemas']['Session'];
export type BAOrganization = BAComponents['schemas']['Organization'];

// Configure base URL from env with sensible default for local dev
const BASE_URL: string = (import.meta as any).env?.VITE_API_URL || 'http://localhost:3001';

// Initialize the Better Auth client and enable organization features
const baseClient = createAuthClient({
  baseURL: BASE_URL,
  plugins: [organizationClient()],
});

// Compatibility shim: add getSession() to match existing usage in the app
async function getSession(): Promise<{ session: BASession; user: BAUser }> {
  const response = await fetch(`${BASE_URL}/get-session`, {
    method: 'GET',
    credentials: 'include',
  });
  if (!response.ok) {
    const message = await response.text();
    const error: any = new Error(message || `HTTP ${response.status}`);
    error.status = response.status;
    throw error;
  }
  return (await response.json()) as { session: BASession; user: BAUser };
}

// Export a single client object that includes Better Auth methods plus our shim
export const authClient = Object.assign(baseClient, { getSession });


