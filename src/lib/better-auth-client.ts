import type { components as BAComponents } from '@/types/better-auth-built-in-api';
import { authClient } from '@/lib/auth-client';
import { API_CONFIG } from '@/lib/api-config';

// Align base URL with API client/auth server to ensure cookies are sent to the same origin
const BASE_URL: string = API_CONFIG.BASE_URL;

type BAUser = BAComponents['schemas']['User'];
type BASession = BAComponents['schemas']['Session'];
export type BAOrganization = BAComponents['schemas']['Organization'];

export interface SignInWithEmailInput {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignUpWithEmailInput {
  email: string;
  password: string;
  name?: string;
  rememberMe?: boolean;
}

export interface GetSessionResponse {
  session: BASession;
  user: BAUser;
}

export type SignInWithEmailResponse =
  | { redirect: false; token?: string | null; user: BAUser; session?: BASession | null }
  | { redirect: true; url: string; token?: null };

export type SignUpWithEmailResponse =
  | { redirect: false; token?: string | null; user: BAUser; session?: BASession | null }
  | { redirect: true; url: string; token?: null };

export const betterAuthClient = {
  // Auth
  async getSession(): Promise<GetSessionResponse> {
    const res: any = await authClient.getSession();
    // Ensure we return the expected shape regardless of underlying client typing
    const session = res?.session as BASession;
    const user = res?.user as BAUser;
    return { session, user };
  },

  async signInWithEmail(input: SignInWithEmailInput): Promise<SignInWithEmailResponse> {
    const result: any = await (authClient as any).signIn.email({
      email: input.email,
      password: input.password,
      rememberMe: input.rememberMe ?? true,
    });
    if (result?.error) throw new Error(result.error.message || 'Sign in failed');
    // Forward token if provided by backend so callers can optimistically hydrate
    return {
      redirect: false,
      token: (result?.data?.token as string) ?? null,
      user: (result?.data?.user as BAUser) ?? ({} as BAUser),
      session: (result?.data?.session as BASession) ?? null,
    };
  },

  async signUpWithEmail(input: SignUpWithEmailInput): Promise<SignUpWithEmailResponse> {
    const result: any = await (authClient as any).signUp.email({
      email: input.email,
      password: input.password,
      name: input.name ?? input.email,
      rememberMe: input.rememberMe ?? true,
    });
    if (result?.error) throw new Error(result.error.message || 'Sign up failed');
    // Forward token if provided by backend so callers can optimistically hydrate
    return {
      redirect: false,
      token: (result?.data?.token as string) ?? null,
      user: (result?.data?.user as BAUser) ?? ({} as BAUser),
      session: (result?.data?.session as BASession) ?? null,
    };
  },

  async signOut(): Promise<{ message?: string }> {
    const result: any = await (authClient as any).signOut();
    if (result?.error) throw new Error(result.error.message || 'Sign out failed');
    return { message: 'Signed out' };
  },

  // Organizations (Workspaces)
  async listOrganizations(): Promise<BAOrganization[]> {
    const result: any = await (authClient as any).organization.list();
    if (result?.error) throw new Error(result.error.message || 'Failed to list organizations');
    return (result?.data as BAOrganization[]) ?? [];
  },

  async setActiveOrganization(params: { organizationId?: string; organizationSlug?: string }): Promise<{ message?: string }> {
    const result: any = await (authClient as any).organization.setActive(params);
    if (result?.error) throw new Error(result.error.message || 'Failed to set active organization');
    return { message: 'ok' };
  },

  async getActiveMember(): Promise<{ id: string; userId: string; organizationId: string; role: string }> {
    const result: any = await (authClient as any).organization.getActiveMember();
    if (result?.error) throw new Error(result.error.message || 'Failed to get active member');
    return result?.data as { id: string; userId: string; organizationId: string; role: string };
  },

  async listInvitations(): Promise<Array<{ id: string; organizationId: string; email: string; role?: string; status: string; expiresAt: string; inviterId: string }>> {
    const result: any = await (authClient as any).organization.listInvitations();
    if (result?.error) throw new Error(result.error.message || 'Failed to list invitations');
    return (result?.data as any[])?.map((inv) => ({
      id: inv.id,
      organizationId: inv.organizationId,
      email: inv.email,
      role: inv.role,
      status: String(inv.status),
      expiresAt: String(inv.expiresAt),
      inviterId: inv.inviterId,
    })) ?? [];
  },

  async inviteMember(body: { email: string; role: string; organizationId?: string; teamId: string; resend?: boolean }): Promise<{
    id: string;
    email: string;
    role: string;
    organizationId: string;
    inviterId: string;
    status: string;
    expiresAt: string;
  }> {
    const result: any = await (authClient as any).organization.inviteMember({
      email: body.email,
      role: body.role,
      organizationId: body.organizationId,
      resend: body.resend,
    });
    if (result?.error) throw new Error(result.error.message || 'Failed to invite member');
    const inv = result?.data;
    return {
      id: inv.id,
      email: inv.email,
      role: inv.role,
      organizationId: inv.organizationId,
      inviterId: inv.inviterId,
      status: String(inv.status),
      expiresAt: String(inv.expiresAt),
    };
  },

  async resendInvitation(body: { invitationId: string }): Promise<{ message?: string }> {
    // Fallback: Better Auth may not expose resend; call underlying endpoint
    const res = await fetch(`${BASE_URL}/organization/invite-member`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ...body, resend: true }),
      credentials: 'include',
    });
    if (!res.ok) {
      throw new Error(`HTTP ${res.status}`);
    }
    return { message: 'ok' };
  },

  async cancelInvitation(body: { invitationId: string }): Promise<{ message?: string }> {
    const result: any = await (authClient as any).organization.cancelInvitation({ invitationId: body.invitationId });
    if (result?.error) throw new Error(result.error.message || 'Failed to cancel invitation');
    return { message: 'ok' };
  },

  async updateOrganization(body: { organizationId: string; data: Record<string, unknown> }): Promise<{ message?: string }> {
    const result: any = await (authClient as any).organization.update({
      organizationId: body.organizationId,
      data: body.data as any,
    });
    if (result?.error) throw new Error(result.error.message || 'Failed to update organization');
    return { message: 'ok' };
  },
};


