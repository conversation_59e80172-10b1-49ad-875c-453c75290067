import React, { useMemo, useState, useEffect } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";

// UI
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// (No popover in v3 sidebar; intentionally omitted)
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadingState, Spinner } from "@/components/ui/spinner";

// Icons
import {
  Plus,
  Search,
  SlidersHorizontal,
  X,
  FilterX,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Eye,
  Edit,
  Building2,
  MapPin,
  DollarSign,
  Users,
  Clock,
  Grid3X3,
  List,
  Upload,
} from "lucide-react";

// App
import { useListings } from "@/hooks/useQueryApi";
import { formatCurrencyAbbreviated } from "@/lib/formatters";
import CsvImportModal2 from "@/components/forms/CsvImportModal2";
import { cn } from "@/lib/utils";

// Types
interface ListingFilters {
  status: string;
  industry: string;
  minPrice: string;
  maxPrice: string;
  location: string;
  assignedTo: string;
  sortBy: string;
  sortOrder: string;
}

// Sort options
const sortOptions = [
  { value: "created_at:desc", label: "Newest First", icon: ArrowDown },
  { value: "created_at:asc", label: "Oldest First", icon: ArrowUp },
  { value: "asking_price:desc", label: "Price: High to Low", icon: ArrowDown },
  { value: "asking_price:asc", label: "Price: Low to High", icon: ArrowUp },
  { value: "business_name:asc", label: "Name: A-Z", icon: ArrowUp },
  { value: "business_name:desc", label: "Name: Z-A", icon: ArrowDown },
];

type ViewMode = "card" | "table";

const V3_Listings: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [viewMode, setViewMode] = useState<ViewMode>("card");
  const [filters, setFilters] = useState<ListingFilters>({
    status: "all",
    industry: "all",
    minPrice: "",
    maxPrice: "",
    location: "",
    assignedTo: "all",
    sortBy: "created_at",
    // Keep current behavior (asc as in V2)
    sortOrder: "asc",
  });
  const itemsPerPage = 12;
  const navigate = useNavigate();

  const currentSortValue = `${filters.sortBy}:${filters.sortOrder}`;
  const currentSortOption =
    sortOptions.find((o) => o.value === currentSortValue) || sortOptions[0];

  const queryParams = useMemo(() => {
    const params: any = {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder,
    };

    if (filters.status && filters.status !== "all") params.status = filters.status;
    if (filters.industry && filters.industry !== "all") params.industry = filters.industry;
    if (filters.location) params.location = filters.location;
    if (filters.assignedTo && filters.assignedTo !== "all")
      params.assignedTo = filters.assignedTo;

    if (filters.minPrice) {
      const minPrice = parseFloat(filters.minPrice.replace(/[,$]/g, ""));
      if (!isNaN(minPrice)) params.minPrice = minPrice;
    }
    if (filters.maxPrice) {
      const maxPrice = parseFloat(filters.maxPrice.replace(/[,$]/g, ""));
      if (!isNaN(maxPrice)) params.maxPrice = maxPrice;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, filters]);

  const { listings, pagination, loading, error, isRefetching, refetch } =
    useListings(queryParams);

  const totalPages = pagination.pages || 1;

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filters]);

  const updateFilter = (key: keyof ListingFilters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split(":");
    setFilters((prev) => ({ ...prev, sortBy, sortOrder }));
  };

  const clearFilters = () => {
    setFilters({
      status: "all",
      industry: "all",
      minPrice: "",
      maxPrice: "",
      location: "",
      assignedTo: "all",
      sortBy: "created_at",
      sortOrder: "desc",
    });
  };

  const hasActiveFilters = Object.entries(filters).some(
    ([key, value]) => key !== "sortBy" && key !== "sortOrder" && value !== "" && value !== "all"
  );
  const activeFilterCount = Object.entries(filters).filter(
    ([key, value]) => key !== "sortBy" && key !== "sortOrder" && value !== "" && value !== "all"
  ).length;

  const handlePageChange = (page: number) => setCurrentPage(page);
  const handleCardClick = (listingId: string) => navigate(`/listings/${listingId}`);
  const handleEditClick = (e: React.MouseEvent, listingId: string) => {
    e.stopPropagation();
    navigate(`/listings/${listingId}/edit`);
  };

  const getDaysOnMarket = (dateListedStr?: string) => {
    if (!dateListedStr) return 0;
    const dateListed = new Date(dateListedStr);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - dateListed.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const AppliedFilterChip: React.FC<{ label: string; onClear: () => void }> = ({
    label,
    onClear,
  }) => (
    <span className="inline-flex items-center gap-1 rounded-full bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 px-2.5 py-1 text-xs border border-blue-200 dark:border-blue-800">
      {label}
      <button
        onClick={onClear}
        className="ml-0.5 rounded-full p-0.5 hover:bg-blue-100 dark:hover:bg-blue-800"
        aria-label="Clear filter"
      >
        <X className="h-3 w-3" />
      </button>
    </span>
  );

  const StatusBadge = ({ status }: { status: string }) => {
    const map: Record<string, { color: string; label: string }> = {
      active: { color: "bg-emerald-500", label: "Active" },
      "under contract": { color: "bg-amber-500", label: "Under Contract" },
      sold: { color: "bg-green-600", label: "Sold" },
      confidential: { color: "bg-purple-600", label: "Confidential" },
      archived: { color: "bg-slate-500", label: "Archived" },
      draft: { color: "bg-blue-500", label: "Draft" },
      pending: { color: "bg-orange-500", label: "Pending" },
      withdrawn: { color: "bg-red-500", label: "Withdrawn" },
    };
    const config = map[status?.toLowerCase?.()] || { color: "bg-slate-300", label: status };
    return (
      <Badge className={`${config.color}/90 text-white border-0 backdrop-blur-sm shadow-sm`}>{config.label}</Badge>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 to-white dark:from-slate-900 dark:to-slate-950">
      {/* Top Bar */}
      <div className="sticky top-0 z-40 border-b border-slate-200/60 dark:border-slate-800/60 bg-white/70 dark:bg-slate-900/70 backdrop-blur-xl">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl font-semibold tracking-tight text-slate-900 dark:text-white">
                Listings
              </h1>
              <p className="text-xs text-slate-600 dark:text-slate-400">Discover and manage business opportunities</p>
            </div>
            <div className="flex items-center gap-2">
              <CsvImportModal2 onSuccess={() => refetch()}>
                <Button variant="outline" className="gap-2 dark:hover:bg-slate-800">
                  <Upload className="h-4 w-4" />
                  Import CSV
                </Button>
              </CsvImportModal2>
              <Button asChild className="gap-2 bg-blue-600 hover:bg-blue-700 text-white">
                <Link to="/listings/new">
                  <Plus className="h-4 w-4" /> New Listing
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-6 grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Sidebar Filters */}
        <aside className="lg:col-span-3">
          <Card className="border-slate-200/60 dark:border-slate-800/60 bg-white/80 dark:bg-slate-900/80 backdrop-blur">
            <CardHeader className="pb-4">
              <CardTitle className="text-base flex items-center gap-2">
                <SlidersHorizontal className="h-4 w-4" /> Filters
                {hasActiveFilters && (
                  <span className="ml-auto text-xs text-slate-500 dark:text-slate-400">
                    {activeFilterCount} active
                  </span>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-5">
              {/* Search */}
              <div>
                <Label className="text-xs">Search</Label>
                <div className="mt-2 relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="Business, industry, or location"
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* Status */}
              <div>
                <Label className="text-xs">Status</Label>
                <div className="mt-2">
                  <Select value={filters.status} onValueChange={(v) => updateFilter("status", v)}>
                    <SelectTrigger className="h-9">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="under contract">Under Contract</SelectItem>
                      <SelectItem value="sold">Sold</SelectItem>
                      <SelectItem value="confidential">Confidential</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Industry */}
              <div>
                <Label className="text-xs">Industry</Label>
                <div className="mt-2">
                  <Select value={filters.industry} onValueChange={(v) => updateFilter("industry", v)}>
                    <SelectTrigger className="h-9">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="Restaurant">Restaurant</SelectItem>
                      <SelectItem value="Technology">Technology</SelectItem>
                      <SelectItem value="Healthcare">Healthcare</SelectItem>
                      <SelectItem value="Retail">Retail</SelectItem>
                      <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="Professional Services">Professional Services</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Price Range */}
              <div>
                <Label className="text-xs">Price Range</Label>
                <div className="mt-2 grid grid-cols-2 gap-2">
                  <Input
                    placeholder="Min"
                    inputMode="decimal"
                    value={filters.minPrice}
                    onChange={(e) => updateFilter("minPrice", e.target.value)}
                  />
                  <Input
                    placeholder="Max"
                    inputMode="decimal"
                    value={filters.maxPrice}
                    onChange={(e) => updateFilter("maxPrice", e.target.value)}
                  />
                </div>
              </div>

              {/* Location */}
              <div>
                <Label className="text-xs">Location</Label>
                <div className="mt-2">
                  <Input
                    placeholder="City, State"
                    value={filters.location}
                    onChange={(e) => updateFilter("location", e.target.value)}
                  />
                </div>
              </div>

              {/* Sort */}
              <div>
                <Label className="text-xs">Sort</Label>
                <div className="mt-2">
                  <Select value={currentSortValue} onValueChange={handleSortChange}>
                    <SelectTrigger className="h-9">
                      <div className="flex items-center gap-2">
                        {currentSortOption.icon && (
                          <currentSortOption.icon className="h-3.5 w-3.5" />
                        )}
                        <span className="truncate">{currentSortOption.label}</span>
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      {sortOptions.map((o) => {
                        const Ico = o.icon;
                        return (
                          <SelectItem key={o.value} value={o.value}>
                            <div className="flex items-center gap-2">
                              <Ico className="h-3.5 w-3.5" /> {o.label}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Actions */}
              <div className="pt-1 flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  disabled={!hasActiveFilters}
                  className="flex-1"
                >
                  <FilterX className="h-4 w-4 mr-1.5" /> Clear
                </Button>
              </div>
            </CardContent>
          </Card>
        </aside>

        {/* Main */}
        <main className="lg:col-span-9 space-y-4">
          {/* Toolbar */}
          <div className="flex flex-wrap items-center justify-between gap-3">
            <div className="text-sm text-slate-600 dark:text-slate-400">
              {loading ? (
                <span>Loading listings…</span>
              ) : (
                <span>
                  Showing {Math.min((currentPage - 1) * itemsPerPage + 1, Math.max(1, pagination.total))}
                  -{Math.min(currentPage * itemsPerPage, pagination.total)} of {pagination.total}
                </span>
              )}
              {isRefetching && <Spinner size="xs" className="ml-2" />}
            </div>
            <div className="flex items-center gap-2">
              <div className="inline-flex rounded-lg border border-slate-200 dark:border-slate-800 p-0.5 bg-white dark:bg-slate-900">
                <Button
                  variant={viewMode === "card" ? "default" : "ghost"}
                  size="sm"
                  className="h-9 px-3"
                  onClick={() => setViewMode("card")}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "table" ? "default" : "ghost"}
                  size="sm"
                  className="h-9 px-3"
                  onClick={() => setViewMode("table")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Applied filters chips */}
          {hasActiveFilters && (
            <div className="flex flex-wrap items-center gap-2">
              {filters.status !== "all" && (
                <AppliedFilterChip label={`Status: ${filters.status}`} onClear={() => updateFilter("status", "all")} />
              )}
              {filters.industry !== "all" && (
                <AppliedFilterChip label={`Industry: ${filters.industry}`} onClear={() => updateFilter("industry", "all")} />
              )}
              {filters.location && (
                <AppliedFilterChip label={`Location: ${filters.location}`} onClear={() => updateFilter("location", "")} />
              )}
              {filters.minPrice && (
                <AppliedFilterChip label={`Min: ${filters.minPrice}`} onClear={() => updateFilter("minPrice", "")} />
              )}
              {filters.maxPrice && (
                <AppliedFilterChip label={`Max: ${filters.maxPrice}`} onClear={() => updateFilter("maxPrice", "")} />
              )}
              {filters.assignedTo !== "all" && (
                <AppliedFilterChip label={`Assigned: ${filters.assignedTo}`} onClear={() => updateFilter("assignedTo", "all")} />
              )}
              <Button variant="ghost" size="sm" onClick={clearFilters} className="h-8">
                Clear all
              </Button>
            </div>
          )}

          {/* Error */}
          {error && (
            <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
              <AlertDescription className="text-red-800 dark:text-red-200">
                Failed to load listings.{' '}
                <Button variant="link" className="p-0 h-auto text-red-600 dark:text-red-400" onClick={() => refetch()}>
                  Try again
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Content */}
          {loading ? (
            <div className="flex items-center justify-center py-20">
              <LoadingState message="Loading your listings…" spinnerSize="lg" spinnerColor="muted" />
            </div>
          ) : listings.length === 0 ? (
            <Card className="border-dashed">
              <CardContent className="py-14 text-center">
                <div className="inline-flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20 p-4 mb-4">
                  <Building2 className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-lg font-medium mb-1">No listings found</div>
                <div className="text-slate-600 dark:text-slate-400 mb-6">
                  {searchTerm || hasActiveFilters
                    ? "Adjust your search or filters."
                    : "Create your first business listing."}
                </div>
                {!searchTerm && !hasActiveFilters && (
                  <Button asChild className="gap-2 bg-blue-600 hover:bg-blue-700 text-white">
                    <Link to="/listings/new">
                      <Plus className="h-4 w-4" /> Create Listing
                    </Link>
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <>
              {viewMode === "card" ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-5">
                  {listings.map((listing: any) => (
                    <Card
                      key={listing.id}
                      onClick={() => handleCardClick(listing.id)}
                      className="cursor-pointer border-slate-200/60 dark:border-slate-800/60 bg-white/80 dark:bg-slate-900/80 hover:shadow-lg transition-all group"
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between gap-2 mb-1">
                          <StatusBadge status={listing.status} />
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100"
                            onClick={(e) => handleEditClick(e, listing.id)}
                            title="Edit listing"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                        <CardTitle className="text-lg font-semibold line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                          {listing.businessName}
                        </CardTitle>
                        <div className="text-sm text-slate-600 dark:text-slate-400 flex items-center gap-2">
                          <Building2 className="h-4 w-4" /> {listing.industry}
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0 space-y-3">
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-emerald-600" />
                          <span className="text-lg font-semibold">
                            {formatCurrencyAbbreviated(listing.askingPrice)}
                          </span>
                        </div>
                        <div className="text-sm text-slate-600 dark:text-slate-400 flex items-center gap-2">
                          <MapPin className="h-4 w-4" /> {listing.generalLocation}
                        </div>
                        {listing.annualRevenue && (
                          <div className="text-sm text-slate-600 dark:text-slate-400 flex items-center gap-2">
                            <Users className="h-4 w-4" /> Revenue: {formatCurrencyAbbreviated(listing.annualRevenue)}
                          </div>
                        )}
                        <div className="text-sm text-slate-600 dark:text-slate-400 flex items-center gap-2">
                          <Clock className="h-4 w-4" /> {getDaysOnMarket(listing.dateListed)} days on market
                        </div>
                        <div className="pt-3 flex gap-2 border-t border-slate-200/60 dark:border-slate-800/60">
                          <Button
                            variant="outline"
                            className="flex-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCardClick(listing.id);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" /> View
                          </Button>
                          <Button
                            variant="outline"
                            className="flex-1"
                            onClick={(e) => handleEditClick(e, listing.id)}
                          >
                            <Edit className="h-4 w-4 mr-2" /> Edit
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card className="overflow-hidden">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Industry</TableHead>
                          <TableHead>Price</TableHead>
                          <TableHead>Location</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="w-24" />
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {listings.map((listing: any) => (
                          <TableRow key={listing.id} className="cursor-pointer" onClick={() => handleCardClick(listing.id)}>
                            <TableCell className="font-medium">
                              <span className="hover:text-blue-600 dark:hover:text-blue-400">
                                {listing.businessName}
                              </span>
                            </TableCell>
                            <TableCell>{listing.industry}</TableCell>
                            <TableCell>{formatCurrencyAbbreviated(listing.askingPrice)}</TableCell>
                            <TableCell>{listing.generalLocation}</TableCell>
                            <TableCell>
                              <StatusBadge status={listing.status} />
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={(e) => handleEditClick(e, listing.id)}
                                title="Edit listing"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </Card>
              )}

              {totalPages > 1 && (
                <div className="flex items-center justify-between pt-2">
                  <div className="text-sm text-slate-600 dark:text-slate-400">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="outline"
                      className="h-9"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ArrowLeft className="h-4 w-4 mr-1" /> Prev
                    </Button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + Math.max(1, currentPage - 2);
                      if (page > totalPages) return null;
                      return (
                        <Button
                          key={page}
                          variant={page === currentPage ? "default" : "outline"}
                          className={cn(
                            "h-9 w-9 p-0",
                            page === currentPage
                              ? "bg-blue-600 text-white"
                              : ""
                          )}
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Button>
                      );
                    })}
                    <Button
                      variant="outline"
                      className="h-9"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </main>
      </div>
    </div>
  );
};

export default V3_Listings;
