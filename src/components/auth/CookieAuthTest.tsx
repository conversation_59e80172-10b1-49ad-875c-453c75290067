import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export const CookieAuthTest: React.FC = () => {
  const { signIn, signOut, refreshSession, user, loading, error } = useAuth();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [isTestingRefresh, setIsTestingRefresh] = useState(false);

  const handleSignIn = async () => {
    try {
      await signIn(email, password);
    } catch (err) {
      console.error('Sign in failed:', err);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (err) {
      console.error('Sign out failed:', err);
    }
  };

  const handleRefresh = async () => {
    setIsTestingRefresh(true);
    try {
      await refreshSession();
    } catch (err) {
      console.error('Refresh failed:', err);
    } finally {
      setIsTestingRefresh(false);
    }
  };

  const checkCookies = () => {
    const cookies = document.cookie;
    console.log('Current cookies:', cookies);
    
    // Note: We can't access httpOnly cookies from JavaScript
    // This will only show non-httpOnly cookies
    return cookies;
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Cookie-Based Authentication Test</CardTitle>
        <CardDescription>
          Test the new httpOnly cookie implementation for refresh tokens
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Authentication Status */}
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Authentication Status</h3>
          <div className="space-y-1 text-sm">
            <div>User: {user ? `${user.email} (ID: ${user.id})` : 'Not authenticated'}</div>
            <div>Loading: {loading ? 'Yes' : 'No'}</div>
            <div>Error: {error ? error.message : 'None'}</div>
          </div>
        </div>

        {/* Sign In Form */}
        {!user && (
          <div className="space-y-4">
            <h3 className="font-semibold">Sign In</h3>
            <div className="space-y-2">
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <Input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <Button onClick={handleSignIn} disabled={loading}>
                Sign In
              </Button>
            </div>
          </div>
        )}

        {/* Authenticated Actions */}
        {user && (
          <div className="space-y-4">
            <h3 className="font-semibold">Authenticated Actions</h3>
            <div className="flex gap-2">
              <Button onClick={handleSignOut} disabled={loading}>
                Sign Out
              </Button>
              <Button 
                onClick={handleRefresh} 
                disabled={loading || isTestingRefresh}
                variant="outline"
              >
                {isTestingRefresh ? 'Refreshing...' : 'Test Refresh'}
              </Button>
            </div>
          </div>
        )}

        {/* Cookie Information */}
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Cookie Information</h3>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Visible Cookies:</strong>
              <pre className="mt-1 p-2 bg-background rounded text-xs overflow-auto">
                {checkCookies() || 'No visible cookies'}
              </pre>
            </div>
            <div className="text-muted-foreground">
              Note: httpOnly cookies (like refresh_token) cannot be accessed by JavaScript
              and won't appear above. This is the security feature working correctly.
            </div>
          </div>
        </div>

        {/* Implementation Notes */}
        <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
          <h3 className="font-semibold mb-2">Implementation Notes</h3>
          <div className="text-sm space-y-1">
            <div>✓ <strong>Refresh Token:</strong> Stored in httpOnly, secure cookie by backend</div>
            <div>✓ <strong>Access Token:</strong> Stored in React state (memory only)</div>
            <div>✓ <strong>Credentials:</strong> All requests include credentials: 'include'</div>
            <div>✓ <strong>Security:</strong> XSS protection via httpOnly, CSRF protection via SameSite=Strict</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};