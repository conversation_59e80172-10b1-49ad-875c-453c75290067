// Enums for multi-tenant system
export enum UserRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer'
}

export enum Permission {
  // Workspace management
  MANAGE_WORKSPACE = 'manage_workspace',
  VIEW_WORKSPACE_SETTINGS = 'view_workspace_settings',
  MANAGE_BILLING = 'manage_billing',
  
  // Team management
  MANAGE_TEAM = 'manage_team',
  INVITE_MEMBERS = 'invite_members',
  REMOVE_MEMBERS = 'remove_members',
  ASSIGN_ROLES = 'assign_roles',
  
  // Listing management
  CREATE_LISTINGS = 'create_listings',
  EDIT_LISTINGS = 'edit_listings',
  DELETE_LISTINGS = 'delete_listings',
  VIEW_LISTINGS = 'view_listings',
  ASSIGN_LISTINGS = 'assign_listings',
  
  // Collaboration
  ADD_INTERNAL_NOTES = 'add_internal_notes',
  VIEW_INTERNAL_NOTES = 'view_internal_notes',
  MENTION_TEAM_MEMBERS = 'mention_team_members',
  VIEW_ACTIVITY_FEED = 'view_activity_feed'
}

export enum SubscriptionPlan {
  TRIAL = 'trial',
  BASIC = 'basic',
  PRO = 'pro',
  ENTERPRISE = 'enterprise'
}

export enum WorkspaceStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  TRIAL = 'trial',
  CANCELLED = 'cancelled'
}

export enum CompanyType {
  INDIVIDUAL = 'individual',
  TEAM = 'team',
  FIRM = 'firm'
}

// Multi-tenant interfaces
export interface Workspace {
  id: string;
  company_name: string;
  company_type: CompanyType;
  subscription_plan: SubscriptionPlan;
  domain?: string;
  logo_url?: string;
  primary_color: string;
  address?: string;
  phone?: string;
  website?: string;
  license_number?: string;
  specialties: string[];
  target_markets: string[];
  status: WorkspaceStatus;
  trial_ends_at?: string;
  onboarding_completed: boolean;
  onboarding_step: number;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  workspace_id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  phone?: string;
  license_number?: string;
  bio?: string;
  avatar_url?: string;
  specialties: string[];
  is_active: boolean;
  invited_at?: string;
  joined_at?: string;
  invited_by?: string;
  created_at: string;
  updated_at: string;
}

export interface WorkspaceInvitation {
  id: string;
  workspace_id: string;
  email: string;
  role: Exclude<UserRole, UserRole.OWNER>; // Owners cannot be invited
  invited_by: string;
  token: string;
  expires_at: string;
  accepted_at?: string;
  created_at: string;
}

export interface InternalNote {
  id: string;
  content: string;
  created_by: string;
  created_at: string;
  mentions: string[];
}

export interface Listing {
  id: string; // Primary key
  workspaceId: string;
  createdBy: string;
  assignedTo?: string;
  businessName: string;
  industry: string;
  askingPrice: string; // API returns as string
  cashFlowSde: string; // API returns as string
  annualRevenue: string; // API returns as string
  status: 'Active' | 'Under Contract' | 'Sold' | 'Confidential' | 'Archived' | 'draft';
  generalLocation: string;
  yearEstablished: number;
  employees: number;
  ownerHoursWeek: number;
  dateListed: string | null;
  daysListed: number | null;
  title: string;
  description: string;
  price: string; // API returns as string
  teamVisibility: 'all' | 'assigned' | 'custom';
  commissionRate?: number;
  images?: string[];
  documents?: string[];
  createdAt: string;
  updatedAt: string;
  // Legacy support - keep internal notes structure
  internal_notes?: InternalNote[];
}

export interface ListingDetails {
  listing_id: string; // Foreign key to Listing
  business_description: string;
  brief_description: string;
  financial_details: {
    revenue_2023: number;
    ebitda: number;
    assets_included: string;
    inventory_value: number;
  };
  operations: {
    business_model: string;
    key_features: string;
    competitive_advantages: string;
  };
  growth_opportunities: string;
  reason_for_sale: string;
  training_period: string;
  support_type: string;
  financing_available: boolean;
  equipment_highlights: string;
  supplier_relationships: string;
  real_estate_status: string;
  lease_details: string;
}

export interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  preferred_contact_method?: string;
}

export interface KPI {
  title: string;
  value: string | number;
  description?: string;
  trend?: {
    value: number;
    positive: boolean;
  };
}

export interface Portfolio {
  id: string;
  name: string;
  description?: string;
  listing_ids: string[];
  created_at: string;
  updated_at: string;
  template?: string;
}

// Authentication and context types
export interface AuthContextType {
  user: any | null; // Supabase User type
  profile: UserProfile | null;
  workspace: Workspace | null;
  loading: boolean;
  error: AuthError | null;
  accessToken: string | null;
  signIn: (email: string, password: string, workspaceId?: string) => Promise<void>;
  signUp: (data: SignUpData) => Promise<void>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<string | void>;
  switchWorkspace: (workspaceId: string) => Promise<void>;
  clearError: () => void;
}

export interface WorkspaceContextType {
  workspace: Workspace | null;
  teamMembers: UserProfile[];
  invitations: WorkspaceInvitation[];
  loading: boolean;
  error: WorkspaceError | null;
  updateWorkspace: (data: Partial<Workspace>) => Promise<void>;
  inviteTeamMember: (email: string, role: UserRole, redirectTo: string) => Promise<void>;
  removeTeamMember: (userId: string) => Promise<void>;
  updateMemberRole: (userId: string, role: UserRole) => Promise<void>;
  resendInvitation: (invitationId: string) => Promise<void>;
  revokeInvitation: (invitationId: string) => Promise<void>;
  refreshTeamData: () => Promise<void>;
  clearError: () => void;
}

export interface PermissionsContextType {
  userRole: UserRole | null;
  permissions: Permission[];
  hasPermission: (permission: Permission) => boolean;
  canAccessFeature: (feature: string) => boolean;
  canManageTeam: () => boolean;
  canManageBilling: () => boolean;
  canEditListing: (listingId: string) => boolean;
  canViewWorkspaceSettings: () => boolean;
  canInviteMembers: () => boolean;
  canRemoveMembers: () => boolean;
  canAssignRoles: () => boolean;
  loading: boolean;
  error: PermissionError | null;
}

// Form data types
export interface SignUpData {
  email: string;
  password: string;
  confirmPassword: string;
  first_name: string;
  last_name: string;
  company_name: string;
  company_type: CompanyType;
  phone?: string;
  license_number?: string;
  website?: string;
  address?: string;
  terms_accepted: boolean;
  marketing_consent?: boolean;
}

export interface InvitationData {
  email: string;
  role: Exclude<UserRole, UserRole.OWNER>;
  first_name?: string;
  last_name?: string;
  message?: string;
  expires_in_days?: number;
}

export interface InvitationAcceptanceData {
  token: string;
  first_name: string;
  last_name: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  license_number?: string;
  bio?: string;
}

// Onboarding types
export enum OnboardingStepId {
  COMPANY_PROFILE = 1,
  MARKET_FOCUS = 2,
  TEAM_SETUP = 3,
  SAMPLE_LISTING = 4,
  PREFERENCES = 5
}

export interface OnboardingStep {
  id: OnboardingStepId;
  title: string;
  description: string;
  completed: boolean;
  required: boolean;
  skippable: boolean;
}

export interface OnboardingData {
  company_profile?: {
    logo_url?: string;
    primary_color?: string;
    description?: string;
    website?: string;
    address?: string;
  };
  market_focus?: {
    target_markets: string[];
    specialties: string[];
    price_ranges?: string[];
    service_areas?: string[];
  };
  team_setup?: {
    initial_invitations: InvitationData[];
  };
  preferences?: {
    notifications?: boolean;
    dashboard_layout?: string;
  };
}

// Error types
export enum AuthErrorCode {
  INVALID_CREDENTIALS = 'invalid_credentials',
  EMAIL_NOT_VERIFIED = 'email_not_verified',
  ACCOUNT_LOCKED = 'account_locked',
  SESSION_EXPIRED = 'session_expired',
  WEAK_PASSWORD = 'weak_password',
  EMAIL_ALREADY_EXISTS = 'email_already_exists',
  SIGNUP_DISABLED = 'signup_disabled',
  NETWORK_ERROR = 'network_error',
  UNKNOWN_ERROR = 'unknown_error'
}

export enum WorkspaceErrorCode {
  WORKSPACE_NOT_FOUND = 'workspace_not_found',
  WORKSPACE_SUSPENDED = 'workspace_suspended',
  TRIAL_EXPIRED = 'trial_expired',
  SUBSCRIPTION_REQUIRED = 'subscription_required',
  TEAM_LIMIT_EXCEEDED = 'team_limit_exceeded',
  LISTING_LIMIT_EXCEEDED = 'listing_limit_exceeded',
  STORAGE_LIMIT_EXCEEDED = 'storage_limit_exceeded',
  FEATURE_NOT_AVAILABLE = 'feature_not_available',
  WORKSPACE_ACCESS_DENIED = 'workspace_access_denied'
}

export enum PermissionErrorCode {
  INSUFFICIENT_PERMISSIONS = 'insufficient_permissions',
  ROLE_REQUIRED = 'role_required',
  FEATURE_RESTRICTED = 'feature_restricted',
  RESOURCE_ACCESS_DENIED = 'resource_access_denied',
  ACTION_NOT_ALLOWED = 'action_not_allowed'
}

export interface AuthError {
  code: AuthErrorCode;
  message: string;
  field?: string;
  details?: any;
  timestamp: string;
}

export interface WorkspaceError {
  code: WorkspaceErrorCode;
  message: string;
  workspace_id?: string;
  required_plan?: SubscriptionPlan;
  current_usage?: number;
  limit?: number;
  details?: any;
  timestamp: string;
}

export interface PermissionError {
  code: PermissionErrorCode;
  message: string;
  required_permission?: Permission;
  required_role?: UserRole;
  current_role?: UserRole;
  resource_id?: string;
  details?: any;
  timestamp: string;
}

// API response types
export interface ApiResponse<T> {
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  success: boolean;
}

// Authentication state and session types
export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: any; // Supabase User type
}

export interface SignInCredentials {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface PasswordResetData {
  email: string;
}

export interface PasswordUpdateData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

// Workspace selection and switching
export interface WorkspaceSelection {
  workspace_id: string;
  workspace_name: string;
  user_role: UserRole;
  is_active: boolean;
}

export interface MultiWorkspaceUser {
  user_id: string;
  workspaces: WorkspaceSelection[];
  default_workspace_id?: string;
}

// Feature flags and subscription limits
export interface SubscriptionLimits {
  max_team_members: number;
  max_listings: number;
  max_storage_gb: number;
  features: string[];
}

export interface FeatureAccess {
  [key: string]: boolean;
}

// Context loading states
export interface LoadingState {
  auth: boolean;
  workspace: boolean;
  permissions: boolean;
  teamMembers: boolean;
  invitations: boolean;
}

// Validation error types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface FormErrors {
  [key: string]: ValidationError[];
}

// Listing Form Types
export interface ListingFormData {
  // Basic Information
  businessName: string;
  industry: string;
  askingPrice: string;
  cashFlow: string;
  status: string;
  annualRevenue: string;
  location: string;
  yearEstablished: string;
  employees: string;
  ownerHours: string;

  // Business Overview
  businessDescription: string;
  briefDescription: string;

  // Financial Details
  revenue2023: string;
  ebitda2023: string;
  inventoryValue: string;
  realEstateStatus: string;
  assetsIncluded: string;
  leaseDetails: string;

  // Operations
  businessModel: string;
  keyFeatures: string;
  competitiveAdvantages: string;
  customerBase: string;

  // Growth & Sale Information
  growthOpportunities: string;
  reasonForSale: string;
  trainingPeriod: string;
  supportType: string;
  financingAvailable: boolean;

  // Additional Details
  equipmentHighlights: string;
  supplierRelationships: string;
  keyEmployeeInfo: string;
  specialNotes: string;
}