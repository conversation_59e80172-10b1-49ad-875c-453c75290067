import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { betterAuthClient, type BAOrganization } from '@/lib/better-auth-client';

interface UseWorkspaceSwitcherResult {
  availableWorkspaces: Array<{
    workspace_id: string;
    workspace_name: string;
    company_name: string;
    user_role: string;
    is_active: boolean;
    status: string;
    subscription_plan: string;
    logo_url?: string;
    primary_color?: string;
  }>;
  loading: boolean;
  error: string | null;
  switchToWorkspace: (workspaceId: string) => Promise<void>;
  refreshWorkspaces: () => Promise<void>;
  isCurrentWorkspace: (workspaceId: string) => boolean;
}

export const useWorkspaceSwitcher = (): UseWorkspaceSwitcherResult => {
  const { accessToken, workspace, switchWorkspace } = useAuth();
  
  const [availableWorkspaces, setAvailableWorkspaces] = useState<UseWorkspaceSwitcherResult['availableWorkspaces']>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchWorkspaces = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Use better-auth organizations list and map to UI-expected shape
      const orgs = await betterAuthClient.listOrganizations();
      const mapped = (orgs || []).map((o: BAOrganization) => ({
        workspace_id: o.id!,
        workspace_name: o.name,
        company_name: o.name,
        user_role: 'member',
        is_active: workspace?.id === o.id,
        status: 'active',
        subscription_plan: 'basic',
        logo_url: undefined,
        primary_color: undefined,
      }));
      setAvailableWorkspaces(mapped);
    } catch (error: any) {
      console.error('Error fetching workspaces:', error);
      setError(error.message || 'Failed to fetch available workspaces');
    } finally {
      setLoading(false);
    }
  }, [workspace?.id]);

  // Fetch workspaces on mount and when accessToken changes
  useEffect(() => {
    fetchWorkspaces();
  }, [fetchWorkspaces]);

  const switchToWorkspace = useCallback(async (workspaceId: string) => {
    if (!workspaceId || workspaceId === workspace?.id) {
      return;
    }

    try {
      setError(null);
      await switchWorkspace(workspaceId);
      
      // Refresh the workspace list after successful switch
      await fetchWorkspaces();
    } catch (error: any) {
      console.error('Error switching workspace:', error);
      setError(error.message || 'Failed to switch workspace');
      throw error; // Re-throw so components can handle it
    }
  }, [workspace?.id, switchWorkspace, fetchWorkspaces]);

  const refreshWorkspaces = useCallback(async () => {
    await fetchWorkspaces();
  }, [fetchWorkspaces]);

  const isCurrentWorkspace = useCallback((workspaceId: string) => {
    return workspaceId === workspace?.id;
  }, [workspace?.id]);

  return {
    availableWorkspaces,
    loading,
    error,
    switchToWorkspace,
    refreshWorkspaces,
    isCurrentWorkspace,
  };
}; 